2025-07-30 05:32:52,635 - EnhancedTimeframeProcessor - INFO - 🧪 测试模式：检查处理器初始化...
2025-07-30 05:32:52,635 - EnhancedTimeframeProcessor - INFO - 🚀 初始化增强多时间周期处理器...
2025-07-30 05:32:53,136 - EnhancedTimeframeProcessor - INFO - ✅ 增强多时间周期处理器初始化完成
2025-07-30 05:32:53,136 - EnhancedTimeframeProcessor - INFO - ✅ 处理器初始化成功
2025-07-30 05:32:53,136 - EnhancedTimeframeProcessor - INFO - 🔄 手动触发一次处理...
2025-07-30 05:32:53,157 - EnhancedTimeframeProcessor - INFO - 📊 处理摘要: {'total_processed': 1, 'successful_processed': 1, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 5, 32, 53, 137062)}
2025-07-30 06:19:36,371 - EnhancedTimeframeProcessor - INFO - 🧪 测试模式：检查处理器初始化...
2025-07-30 06:19:36,372 - EnhancedTimeframeProcessor - INFO - 🚀 初始化增强多时间周期处理器...
2025-07-30 06:19:36,802 - EnhancedTimeframeProcessor - INFO - ✅ 增强多时间周期处理器初始化完成
2025-07-30 06:19:36,802 - EnhancedTimeframeProcessor - INFO - ✅ 处理器初始化成功
2025-07-30 06:19:36,802 - EnhancedTimeframeProcessor - INFO - 🔄 手动触发一次处理...
2025-07-30 06:19:36,835 - EnhancedTimeframeProcessor - INFO - 📊 处理摘要: {'total_processed': 1, 'successful_processed': 1, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 19, 36, 802982)}
2025-07-30 06:19:59,945 - EnhancedTimeframeProcessor - INFO - 🧪 测试模式：检查处理器初始化...
2025-07-30 06:19:59,945 - EnhancedTimeframeProcessor - INFO - 🚀 初始化增强多时间周期处理器...
2025-07-30 06:20:00,387 - EnhancedTimeframeProcessor - INFO - ✅ 增强多时间周期处理器初始化完成
2025-07-30 06:20:00,387 - EnhancedTimeframeProcessor - INFO - ✅ 处理器初始化成功
2025-07-30 06:20:00,387 - EnhancedTimeframeProcessor - INFO - 🔄 手动触发一次处理...
2025-07-30 06:20:00,433 - EnhancedTimeframeProcessor - INFO - 📊 处理摘要: {'total_processed': 1, 'successful_processed': 1, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 20, 0, 387479)}
2025-07-30 06:22:23,049 - EnhancedTimeframeProcessor - INFO - 🧪 测试模式：检查处理器初始化...
2025-07-30 06:22:23,049 - EnhancedTimeframeProcessor - INFO - 🚀 初始化增强多时间周期处理器...
2025-07-30 06:22:23,491 - EnhancedTimeframeProcessor - INFO - ✅ 增强多时间周期处理器初始化完成
2025-07-30 06:22:23,491 - EnhancedTimeframeProcessor - INFO - ✅ 处理器初始化成功
2025-07-30 06:22:23,491 - EnhancedTimeframeProcessor - INFO - 🔄 手动触发一次处理...
2025-07-30 06:22:23,542 - EnhancedTimeframeProcessor - INFO - 📊 处理摘要: {'total_processed': 1, 'successful_processed': 1, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 22, 23, 491661)}
🚀 正常模式暂未实现，请使用 --test 参数
2025-07-30 06:32:51,929 - EnhancedTimeframeProcessor - INFO - 🚀 守护进程模式：启动增强多时间周期处理器...
2025-07-30 06:32:51,929 - EnhancedTimeframeProcessor - INFO - 🚀 初始化增强多时间周期处理器...
2025-07-30 06:32:51,929 - EnhancedTimeframeProcessor - INFO - 🚀 守护进程模式：启动增强多时间周期处理器...
2025-07-30 06:32:51,929 - EnhancedTimeframeProcessor - INFO - 🚀 初始化增强多时间周期处理器...
2025-07-30 06:32:52,386 - EnhancedTimeframeProcessor - INFO - ✅ 增强多时间周期处理器初始化完成
2025-07-30 06:32:52,386 - EnhancedTimeframeProcessor - INFO - ✅ 处理器初始化成功，开始守护进程模式...
2025-07-30 06:32:52,386 - EnhancedTimeframeProcessor - INFO - ✅ 增强多时间周期处理器初始化完成
2025-07-30 06:32:52,386 - EnhancedTimeframeProcessor - INFO - ✅ 处理器初始化成功，开始守护进程模式...
2025-07-30 06:37:53,127 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 11, 'successful_processed': 11, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 37, 53, 84974)}
2025-07-30 06:37:53,127 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 11, 'successful_processed': 11, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 37, 53, 84974)}
2025-07-30 06:42:53,859 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 21, 'successful_processed': 21, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 42, 53, 813034)}
2025-07-30 06:42:53,859 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 21, 'successful_processed': 21, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 42, 53, 813034)}
2025-07-30 06:47:54,676 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 31, 'successful_processed': 31, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 47, 54, 627886)}
2025-07-30 06:47:54,676 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 31, 'successful_processed': 31, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 0.8}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 47, 54, 627886)}
2025-07-30 06:52:55,361 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 41, 'successful_processed': 41, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 52, 55, 322855)}
2025-07-30 06:52:55,361 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 41, 'successful_processed': 41, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 52, 55, 322855)}
2025-07-30 06:57:56,058 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 51, 'successful_processed': 51, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 57, 56, 16464)}
2025-07-30 06:57:56,058 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 51, 'successful_processed': 51, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 6, 57, 56, 16464)}
2025-07-30 07:02:56,750 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 61, 'successful_processed': 61, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 2, 56, 709352)}
2025-07-30 07:02:56,750 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 61, 'successful_processed': 61, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 2, 56, 709352)}
2025-07-30 07:07:57,466 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 71, 'successful_processed': 71, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 7, 57, 418031)}
2025-07-30 07:07:57,466 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 71, 'successful_processed': 71, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 7, 57, 418031)}
2025-07-30 07:12:58,233 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 81, 'successful_processed': 81, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 12, 58, 185759)}
2025-07-30 07:12:58,233 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 81, 'successful_processed': 81, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 12, 58, 185759)}
2025-07-30 07:17:58,937 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 91, 'successful_processed': 91, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 17, 58, 893368)}
2025-07-30 07:17:58,937 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 91, 'successful_processed': 91, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 17, 58, 893368)}
2025-07-30 07:22:59,688 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 101, 'successful_processed': 101, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 22, 59, 633970)}
2025-07-30 07:22:59,688 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 101, 'successful_processed': 101, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 22, 59, 633970)}
2025-07-30 07:28:00,390 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 111, 'successful_processed': 111, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 28, 0, 349327)}
2025-07-30 07:28:00,390 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 111, 'successful_processed': 111, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 28, 0, 349327)}
2025-07-30 07:33:01,087 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 121, 'successful_processed': 121, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 33, 1, 33907)}
2025-07-30 07:33:01,087 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 121, 'successful_processed': 121, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 33, 1, 33907)}
2025-07-30 07:38:01,830 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 131, 'successful_processed': 131, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 38, 1, 756639)}
2025-07-30 07:38:01,830 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 131, 'successful_processed': 131, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 38, 1, 756639)}
2025-07-30 07:43:02,600 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 141, 'successful_processed': 141, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 43, 2, 553691)}
2025-07-30 07:43:02,600 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 141, 'successful_processed': 141, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 43, 2, 553691)}
2025-07-30 07:48:03,345 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 151, 'successful_processed': 151, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 48, 3, 304248)}
2025-07-30 07:48:03,345 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 151, 'successful_processed': 151, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 48, 3, 304248)}
2025-07-30 07:53:04,074 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 161, 'successful_processed': 161, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 53, 4, 12134)}
2025-07-30 07:53:04,074 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 161, 'successful_processed': 161, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 53, 4, 12134)}
2025-07-30 07:58:04,760 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 171, 'successful_processed': 171, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 58, 4, 709533)}
2025-07-30 07:58:04,760 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 171, 'successful_processed': 171, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 7, 58, 4, 709533)}
2025-07-30 08:03:05,510 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 181, 'successful_processed': 181, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 3, 5, 451367)}
2025-07-30 08:03:05,510 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 181, 'successful_processed': 181, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 3, 5, 451367)}
2025-07-30 08:08:06,332 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 191, 'successful_processed': 191, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 8, 6, 255697)}
2025-07-30 08:08:06,332 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 191, 'successful_processed': 191, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 8, 6, 255697)}
2025-07-30 08:13:07,197 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 201, 'successful_processed': 201, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 13, 7, 133820)}
2025-07-30 08:13:07,197 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 201, 'successful_processed': 201, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 13, 7, 133820)}
2025-07-30 08:18:07,956 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 211, 'successful_processed': 211, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 18, 7, 879690)}
2025-07-30 08:18:07,956 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 211, 'successful_processed': 211, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 18, 7, 879690)}
2025-07-30 08:23:08,850 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 221, 'successful_processed': 221, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 23, 8, 781773)}
2025-07-30 08:23:08,850 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 221, 'successful_processed': 221, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 23, 8, 781773)}
2025-07-30 08:28:09,673 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 231, 'successful_processed': 231, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 28, 9, 591221)}
2025-07-30 08:28:09,673 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 231, 'successful_processed': 231, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 28, 9, 591221)}
2025-07-30 08:33:10,763 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 241, 'successful_processed': 241, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 33, 10, 698887)}
2025-07-30 08:33:10,763 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 241, 'successful_processed': 241, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 33, 10, 698887)}
2025-07-30 08:38:11,675 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 251, 'successful_processed': 251, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 38, 11, 603862)}
2025-07-30 08:38:11,675 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 251, 'successful_processed': 251, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 38, 11, 603862)}
2025-07-30 08:43:12,522 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 261, 'successful_processed': 261, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 43, 12, 455035)}
2025-07-30 08:43:12,522 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 261, 'successful_processed': 261, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 43, 12, 455035)}
2025-07-30 08:48:13,361 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 271, 'successful_processed': 271, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 48, 13, 248351)}
2025-07-30 08:48:13,361 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 271, 'successful_processed': 271, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 48, 13, 248351)}
2025-07-30 08:53:14,326 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 281, 'successful_processed': 281, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 53, 14, 264582)}
2025-07-30 08:53:14,326 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 281, 'successful_processed': 281, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 53, 14, 264582)}
2025-07-30 08:58:15,416 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 291, 'successful_processed': 291, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 58, 15, 304522)}
2025-07-30 08:58:15,416 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 291, 'successful_processed': 291, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 8, 58, 15, 304522)}
2025-07-30 09:03:16,333 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 301, 'successful_processed': 301, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 3, 16, 274893)}
2025-07-30 09:03:16,333 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 301, 'successful_processed': 301, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 3, 16, 274893)}
2025-07-30 09:08:17,412 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 311, 'successful_processed': 311, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 8, 17, 358237)}
2025-07-30 09:08:17,412 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 311, 'successful_processed': 311, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 8, 17, 358237)}
2025-07-30 09:13:18,428 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 321, 'successful_processed': 321, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 13, 18, 371262)}
2025-07-30 09:13:18,428 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 321, 'successful_processed': 321, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 13, 18, 371262)}
2025-07-30 09:18:19,424 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 331, 'successful_processed': 331, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 18, 19, 356839)}
2025-07-30 09:18:19,424 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 331, 'successful_processed': 331, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 18, 19, 356839)}
2025-07-30 09:23:20,344 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 341, 'successful_processed': 341, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 23, 20, 292765)}
2025-07-30 09:23:20,344 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 341, 'successful_processed': 341, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 23, 20, 292765)}
2025-07-30 09:28:21,295 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 351, 'successful_processed': 351, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 28, 21, 236990)}
2025-07-30 09:28:21,295 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 351, 'successful_processed': 351, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 28, 21, 236990)}
2025-07-30 09:33:22,142 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 361, 'successful_processed': 361, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 33, 22, 72274)}
2025-07-30 09:33:22,142 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 361, 'successful_processed': 361, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 33, 22, 72274)}
2025-07-30 09:38:22,972 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 371, 'successful_processed': 371, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 38, 22, 905217)}
2025-07-30 09:38:22,972 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 371, 'successful_processed': 371, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 38, 22, 905217)}
2025-07-30 09:43:23,803 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 381, 'successful_processed': 381, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 43, 23, 731874)}
2025-07-30 09:43:23,803 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 381, 'successful_processed': 381, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 43, 23, 731874)}
2025-07-30 09:48:24,641 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 391, 'successful_processed': 391, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 48, 24, 580430)}
2025-07-30 09:48:24,641 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 391, 'successful_processed': 391, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 48, 24, 580430)}
2025-07-30 09:53:25,425 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 401, 'successful_processed': 401, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 53, 25, 361911)}
2025-07-30 09:53:25,425 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 401, 'successful_processed': 401, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 53, 25, 361911)}
2025-07-30 09:58:26,226 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 411, 'successful_processed': 411, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 58, 26, 176554)}
2025-07-30 09:58:26,226 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 411, 'successful_processed': 411, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 9, 58, 26, 176554)}
2025-07-30 10:03:26,972 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 421, 'successful_processed': 421, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 3, 26, 933547)}
2025-07-30 10:03:26,972 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 421, 'successful_processed': 421, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 3, 26, 933547)}
2025-07-30 10:08:27,851 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 431, 'successful_processed': 431, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 8, 27, 732160)}
2025-07-30 10:08:27,851 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 431, 'successful_processed': 431, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 8, 27, 732160)}
2025-07-30 10:13:28,819 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 441, 'successful_processed': 441, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 13, 28, 747895)}
2025-07-30 10:13:28,819 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 441, 'successful_processed': 441, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 13, 28, 747895)}
2025-07-30 10:18:29,864 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 451, 'successful_processed': 451, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 18, 29, 776990)}
2025-07-30 10:18:29,864 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 451, 'successful_processed': 451, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 18, 29, 776990)}
2025-07-30 10:23:30,726 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 461, 'successful_processed': 461, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 23, 30, 667844)}
2025-07-30 10:23:30,726 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 461, 'successful_processed': 461, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 23, 30, 667844)}
2025-07-30 10:28:31,652 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 471, 'successful_processed': 471, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 28, 31, 576523)}
2025-07-30 10:28:31,652 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 471, 'successful_processed': 471, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 28, 31, 576523)}
2025-07-30 10:33:32,626 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 481, 'successful_processed': 481, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 33, 32, 577213)}
2025-07-30 10:33:32,626 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 481, 'successful_processed': 481, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 33, 32, 577213)}
2025-07-30 10:38:33,483 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 491, 'successful_processed': 491, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 38, 33, 426102)}
2025-07-30 10:38:33,483 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 491, 'successful_processed': 491, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 38, 33, 426102)}
2025-07-30 10:43:34,398 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 501, 'successful_processed': 501, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 43, 34, 349279)}
2025-07-30 10:43:34,398 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 501, 'successful_processed': 501, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 43, 34, 349279)}
2025-07-30 10:48:35,324 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 511, 'successful_processed': 511, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 48, 35, 269736)}
2025-07-30 10:48:35,324 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 511, 'successful_processed': 511, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 48, 35, 269736)}
2025-07-30 10:53:36,168 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 521, 'successful_processed': 521, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 53, 36, 105935)}
2025-07-30 10:53:36,168 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 521, 'successful_processed': 521, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 53, 36, 105935)}
2025-07-30 10:58:37,045 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 531, 'successful_processed': 531, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 58, 36, 979702)}
2025-07-30 10:58:37,045 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 531, 'successful_processed': 531, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 10, 58, 36, 979702)}
2025-07-30 11:03:37,998 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 541, 'successful_processed': 541, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 3, 37, 952962)}
2025-07-30 11:03:37,998 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 541, 'successful_processed': 541, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 3, 37, 952962)}
2025-07-30 11:08:38,843 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 551, 'successful_processed': 551, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 8, 38, 788432)}
2025-07-30 11:08:38,843 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 551, 'successful_processed': 551, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 8, 38, 788432)}
2025-07-30 11:13:39,702 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 561, 'successful_processed': 561, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 13, 39, 611018)}
2025-07-30 11:13:39,702 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 561, 'successful_processed': 561, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 13, 39, 611018)}
2025-07-30 11:18:40,549 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 571, 'successful_processed': 571, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 18, 40, 502622)}
2025-07-30 11:18:40,549 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 571, 'successful_processed': 571, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 18, 40, 502622)}
2025-07-30 11:23:41,317 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 581, 'successful_processed': 581, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 23, 41, 271437)}
2025-07-30 11:23:41,317 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 581, 'successful_processed': 581, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 23, 41, 271437)}
2025-07-30 11:28:42,035 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 591, 'successful_processed': 591, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 28, 41, 979814)}
2025-07-30 11:28:42,035 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 591, 'successful_processed': 591, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 28, 41, 979814)}
2025-07-30 11:33:42,792 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 601, 'successful_processed': 601, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 33, 42, 750392)}
2025-07-30 11:33:42,792 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 601, 'successful_processed': 601, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 33, 42, 750392)}
2025-07-30 11:38:43,535 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 611, 'successful_processed': 611, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 38, 43, 485868)}
2025-07-30 11:38:43,535 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 611, 'successful_processed': 611, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 38, 43, 485868)}
2025-07-30 11:43:44,359 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 621, 'successful_processed': 621, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 43, 44, 294099)}
2025-07-30 11:43:44,359 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 621, 'successful_processed': 621, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 43, 44, 294099)}
2025-07-30 11:48:45,189 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 631, 'successful_processed': 631, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 48, 45, 137347)}
2025-07-30 11:48:45,189 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 631, 'successful_processed': 631, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 48, 45, 137347)}
2025-07-30 11:53:45,906 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 641, 'successful_processed': 641, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 53, 45, 865401)}
2025-07-30 11:53:45,906 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 641, 'successful_processed': 641, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 53, 45, 865401)}
2025-07-30 11:58:46,767 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 651, 'successful_processed': 651, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 58, 46, 723957)}
2025-07-30 11:58:46,767 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 651, 'successful_processed': 651, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 11, 58, 46, 723957)}
2025-07-30 12:03:47,681 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 661, 'successful_processed': 661, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 3, 47, 638197)}
2025-07-30 12:03:47,681 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 661, 'successful_processed': 661, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 3, 47, 638197)}
2025-07-30 12:08:48,591 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 671, 'successful_processed': 671, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 8, 48, 543809)}
2025-07-30 12:08:48,591 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 671, 'successful_processed': 671, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 8, 48, 543809)}
2025-07-30 12:13:49,514 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 681, 'successful_processed': 681, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 13, 49, 448051)}
2025-07-30 12:13:49,514 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 681, 'successful_processed': 681, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 13, 49, 448051)}
2025-07-30 12:18:50,465 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 691, 'successful_processed': 691, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 18, 50, 407468)}
2025-07-30 12:18:50,465 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 691, 'successful_processed': 691, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 18, 50, 407468)}
2025-07-30 12:23:51,348 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 701, 'successful_processed': 701, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 23, 51, 292819)}
2025-07-30 12:23:51,348 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 701, 'successful_processed': 701, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 23, 51, 292819)}
2025-07-30 12:28:52,231 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 711, 'successful_processed': 711, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 28, 52, 157937)}
2025-07-30 12:28:52,231 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 711, 'successful_processed': 711, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 28, 52, 157937)}
2025-07-30 12:33:53,071 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 721, 'successful_processed': 721, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 33, 53, 18846)}
2025-07-30 12:33:53,071 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 721, 'successful_processed': 721, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 33, 53, 18846)}
2025-07-30 12:38:53,935 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 731, 'successful_processed': 731, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 38, 53, 893356)}
2025-07-30 12:38:53,935 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 731, 'successful_processed': 731, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 38, 53, 893356)}
2025-07-30 12:43:54,733 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 741, 'successful_processed': 741, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 43, 54, 651868)}
2025-07-30 12:43:54,733 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 741, 'successful_processed': 741, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 43, 54, 651868)}
2025-07-30 12:48:55,550 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 751, 'successful_processed': 751, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 48, 55, 497472)}
2025-07-30 12:48:55,550 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 751, 'successful_processed': 751, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 48, 55, 497472)}
2025-07-30 12:53:56,422 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 761, 'successful_processed': 761, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 53, 56, 343850)}
2025-07-30 12:53:56,422 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 761, 'successful_processed': 761, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 53, 56, 343850)}
2025-07-30 12:58:57,240 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 771, 'successful_processed': 771, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 58, 57, 192794)}
2025-07-30 12:58:57,240 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 771, 'successful_processed': 771, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 12, 58, 57, 192794)}
2025-07-30 13:03:58,045 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 781, 'successful_processed': 781, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 3, 57, 987095)}
2025-07-30 13:03:58,045 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 781, 'successful_processed': 781, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 3, 57, 987095)}
2025-07-30 13:08:58,877 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 791, 'successful_processed': 791, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 8, 58, 830109)}
2025-07-30 13:08:58,877 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 791, 'successful_processed': 791, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 8, 58, 830109)}
2025-07-30 13:13:59,778 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 801, 'successful_processed': 801, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 13, 59, 722243)}
2025-07-30 13:13:59,778 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 801, 'successful_processed': 801, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 13, 59, 722243)}
2025-07-30 13:19:00,625 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 811, 'successful_processed': 811, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 19, 0, 560468)}
2025-07-30 13:19:00,625 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 811, 'successful_processed': 811, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 19, 0, 560468)}
2025-07-30 13:24:01,418 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 821, 'successful_processed': 821, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 24, 1, 350371)}
2025-07-30 13:24:01,418 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 821, 'successful_processed': 821, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 24, 1, 350371)}
2025-07-30 13:29:02,528 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 831, 'successful_processed': 831, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 29, 2, 430819)}
2025-07-30 13:29:02,528 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 831, 'successful_processed': 831, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 29, 2, 430819)}
2025-07-30 13:34:03,563 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 841, 'successful_processed': 841, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 34, 3, 445281)}
2025-07-30 13:34:03,563 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 841, 'successful_processed': 841, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 34, 3, 445281)}
2025-07-30 13:39:04,628 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 851, 'successful_processed': 851, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 39, 4, 498087)}
2025-07-30 13:39:04,628 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 851, 'successful_processed': 851, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 39, 4, 498087)}
2025-07-30 13:44:05,541 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 861, 'successful_processed': 861, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 44, 5, 463743)}
2025-07-30 13:44:05,541 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 861, 'successful_processed': 861, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 44, 5, 463743)}
2025-07-30 13:49:06,534 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 871, 'successful_processed': 871, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 49, 6, 486673)}
2025-07-30 13:49:06,534 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 871, 'successful_processed': 871, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 49, 6, 486673)}
2025-07-30 13:54:07,324 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 881, 'successful_processed': 881, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 54, 7, 290001)}
2025-07-30 13:54:07,324 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 881, 'successful_processed': 881, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 54, 7, 290001)}
2025-07-30 13:59:08,272 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 891, 'successful_processed': 891, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 59, 8, 198248)}
2025-07-30 13:59:08,272 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 891, 'successful_processed': 891, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 13, 59, 8, 198248)}
2025-07-30 14:04:09,214 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 901, 'successful_processed': 901, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 4, 9, 118939)}
2025-07-30 14:04:09,214 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 901, 'successful_processed': 901, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 4, 9, 118939)}
2025-07-30 14:09:10,101 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 911, 'successful_processed': 911, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 9, 10, 45202)}
2025-07-30 14:09:10,101 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 911, 'successful_processed': 911, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 9, 10, 45202)}
2025-07-30 14:14:10,883 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 921, 'successful_processed': 921, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 14, 10, 836803)}
2025-07-30 14:14:10,883 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 921, 'successful_processed': 921, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 14, 10, 836803)}
2025-07-30 14:19:11,803 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 931, 'successful_processed': 931, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 19, 11, 747317)}
2025-07-30 14:19:11,803 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 931, 'successful_processed': 931, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 19, 11, 747317)}
2025-07-30 14:24:12,734 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 941, 'successful_processed': 941, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 24, 12, 682843)}
2025-07-30 14:24:12,734 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 941, 'successful_processed': 941, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 24, 12, 682843)}
2025-07-30 14:29:13,544 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 951, 'successful_processed': 951, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 29, 13, 489972)}
2025-07-30 14:29:13,544 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 951, 'successful_processed': 951, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 29, 13, 489972)}
2025-07-30 14:34:14,428 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 961, 'successful_processed': 961, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 34, 14, 379728)}
2025-07-30 14:34:14,428 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 961, 'successful_processed': 961, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 34, 14, 379728)}
2025-07-30 14:39:15,382 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 971, 'successful_processed': 971, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 39, 15, 308074)}
2025-07-30 14:39:15,382 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 971, 'successful_processed': 971, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 39, 15, 308074)}
2025-07-30 14:44:16,238 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 981, 'successful_processed': 981, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 44, 16, 159965)}
2025-07-30 14:44:16,238 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 981, 'successful_processed': 981, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 44, 16, 159965)}
2025-07-30 14:49:17,338 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 991, 'successful_processed': 991, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 49, 17, 199286)}
2025-07-30 14:49:17,338 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 991, 'successful_processed': 991, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 49, 17, 199286)}
2025-07-30 14:54:18,262 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1001, 'successful_processed': 1001, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 54, 18, 204241)}
2025-07-30 14:54:18,262 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1001, 'successful_processed': 1001, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 54, 18, 204241)}
2025-07-30 14:59:19,166 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1011, 'successful_processed': 1011, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 59, 19, 111713)}
2025-07-30 14:59:19,166 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1011, 'successful_processed': 1011, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 14, 59, 19, 111713)}
2025-07-30 15:04:20,090 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1021, 'successful_processed': 1021, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 4, 19, 983831)}
2025-07-30 15:04:20,090 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1021, 'successful_processed': 1021, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 4, 19, 983831)}
2025-07-30 15:09:21,072 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1031, 'successful_processed': 1031, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 9, 20, 997767)}
2025-07-30 15:09:21,072 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1031, 'successful_processed': 1031, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 9, 20, 997767)}
2025-07-30 15:14:21,905 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1041, 'successful_processed': 1041, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 14, 21, 840800)}
2025-07-30 15:14:21,905 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1041, 'successful_processed': 1041, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 14, 21, 840800)}
2025-07-30 15:19:22,824 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1051, 'successful_processed': 1051, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 19, 22, 781049)}
2025-07-30 15:19:22,824 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1051, 'successful_processed': 1051, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 19, 22, 781049)}
2025-07-30 15:24:23,853 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1061, 'successful_processed': 1061, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 24, 23, 785951)}
2025-07-30 15:24:23,853 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1061, 'successful_processed': 1061, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 24, 23, 785951)}
2025-07-30 15:29:24,748 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1071, 'successful_processed': 1071, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 29, 24, 697643)}
2025-07-30 15:29:24,748 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1071, 'successful_processed': 1071, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 29, 24, 697643)}
2025-07-30 15:34:25,620 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1081, 'successful_processed': 1081, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 34, 25, 570465)}
2025-07-30 15:34:25,620 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1081, 'successful_processed': 1081, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 34, 25, 570465)}
2025-07-30 15:39:26,383 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1091, 'successful_processed': 1091, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 39, 26, 330122)}
2025-07-30 15:39:26,383 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1091, 'successful_processed': 1091, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 39, 26, 330122)}
2025-07-30 15:44:27,121 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1101, 'successful_processed': 1101, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 44, 27, 75040)}
2025-07-30 15:44:27,121 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1101, 'successful_processed': 1101, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 44, 27, 75040)}
2025-07-30 15:49:28,193 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1111, 'successful_processed': 1111, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 49, 28, 133791)}
2025-07-30 15:49:28,193 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1111, 'successful_processed': 1111, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 49, 28, 133791)}
2025-07-30 15:54:29,020 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1121, 'successful_processed': 1121, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 54, 28, 969947)}
2025-07-30 15:54:29,020 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1121, 'successful_processed': 1121, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 54, 28, 969947)}
2025-07-30 15:59:29,897 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1131, 'successful_processed': 1131, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 59, 29, 833915)}
2025-07-30 15:59:29,897 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1131, 'successful_processed': 1131, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 15, 59, 29, 833915)}
2025-07-30 16:04:30,784 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1141, 'successful_processed': 1141, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 4, 30, 725553)}
2025-07-30 16:04:30,784 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1141, 'successful_processed': 1141, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 4, 30, 725553)}
2025-07-30 16:09:31,769 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1151, 'successful_processed': 1151, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 9, 31, 703971)}
2025-07-30 16:09:31,769 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1151, 'successful_processed': 1151, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 9, 31, 703971)}
2025-07-30 16:14:32,805 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1161, 'successful_processed': 1161, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 14, 32, 743278)}
2025-07-30 16:14:32,805 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1161, 'successful_processed': 1161, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 14, 32, 743278)}
2025-07-30 16:19:33,692 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1171, 'successful_processed': 1171, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 19, 33, 648253)}
2025-07-30 16:19:33,692 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1171, 'successful_processed': 1171, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 19, 33, 648253)}
2025-07-30 16:24:34,597 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1181, 'successful_processed': 1181, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 24, 34, 546803)}
2025-07-30 16:24:34,597 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1181, 'successful_processed': 1181, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 24, 34, 546803)}
2025-07-30 16:29:35,407 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1191, 'successful_processed': 1191, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 29, 35, 363232)}
2025-07-30 16:29:35,407 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1191, 'successful_processed': 1191, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 29, 35, 363232)}
2025-07-30 16:34:36,287 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1201, 'successful_processed': 1201, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 34, 36, 228331)}
2025-07-30 16:34:36,287 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1201, 'successful_processed': 1201, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 34, 36, 228331)}
2025-07-30 16:39:37,252 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1211, 'successful_processed': 1211, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 39, 37, 188054)}
2025-07-30 16:39:37,252 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1211, 'successful_processed': 1211, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 39, 37, 188054)}
2025-07-30 16:44:38,050 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1221, 'successful_processed': 1221, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 44, 37, 995835)}
2025-07-30 16:44:38,050 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1221, 'successful_processed': 1221, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 44, 37, 995835)}
2025-07-30 16:49:38,783 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1231, 'successful_processed': 1231, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 49, 38, 738399)}
2025-07-30 16:49:38,783 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1231, 'successful_processed': 1231, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 49, 38, 738399)}
2025-07-30 16:54:39,708 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1241, 'successful_processed': 1241, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 54, 39, 660510)}
2025-07-30 16:54:39,708 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1241, 'successful_processed': 1241, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 54, 39, 660510)}
2025-07-30 16:59:40,830 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1251, 'successful_processed': 1251, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 59, 40, 780036)}
2025-07-30 16:59:40,830 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1251, 'successful_processed': 1251, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 16, 59, 40, 780036)}
2025-07-30 17:04:41,698 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1261, 'successful_processed': 1261, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 4, 41, 653336)}
2025-07-30 17:04:41,698 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1261, 'successful_processed': 1261, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 4, 41, 653336)}
2025-07-30 17:09:42,596 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1271, 'successful_processed': 1271, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 9, 42, 528786)}
2025-07-30 17:09:42,596 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1271, 'successful_processed': 1271, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 9, 42, 528786)}
2025-07-30 17:14:43,470 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1281, 'successful_processed': 1281, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 14, 43, 416128)}
2025-07-30 17:14:43,470 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1281, 'successful_processed': 1281, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 14, 43, 416128)}
2025-07-30 17:19:44,341 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1291, 'successful_processed': 1291, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 19, 44, 277349)}
2025-07-30 17:19:44,341 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1291, 'successful_processed': 1291, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 19, 44, 277349)}
2025-07-30 17:24:45,211 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1301, 'successful_processed': 1301, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 24, 45, 163948)}
2025-07-30 17:24:45,211 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1301, 'successful_processed': 1301, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 24, 45, 163948)}
2025-07-30 17:29:46,018 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1311, 'successful_processed': 1311, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 29, 45, 963727)}
2025-07-30 17:29:46,018 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1311, 'successful_processed': 1311, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 29, 45, 963727)}
2025-07-30 17:34:46,825 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1321, 'successful_processed': 1321, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 34, 46, 767684)}
2025-07-30 17:34:46,825 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1321, 'successful_processed': 1321, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 34, 46, 767684)}
2025-07-30 17:39:47,615 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1331, 'successful_processed': 1331, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 39, 47, 570874)}
2025-07-30 17:39:47,615 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1331, 'successful_processed': 1331, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 39, 47, 570874)}
2025-07-30 17:44:48,439 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1341, 'successful_processed': 1341, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 44, 48, 393229)}
2025-07-30 17:44:48,439 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1341, 'successful_processed': 1341, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 44, 48, 393229)}
2025-07-30 17:49:49,287 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1351, 'successful_processed': 1351, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 49, 49, 241993)}
2025-07-30 17:49:49,287 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1351, 'successful_processed': 1351, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 49, 49, 241993)}
2025-07-30 17:54:50,041 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1361, 'successful_processed': 1361, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 54, 49, 989373)}
2025-07-30 17:54:50,041 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1361, 'successful_processed': 1361, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 54, 49, 989373)}
2025-07-30 17:59:50,851 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1371, 'successful_processed': 1371, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 59, 50, 805511)}
2025-07-30 17:59:50,851 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1371, 'successful_processed': 1371, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 17, 59, 50, 805511)}
2025-07-30 18:04:51,572 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1381, 'successful_processed': 1381, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 4, 51, 526280)}
2025-07-30 18:04:51,572 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1381, 'successful_processed': 1381, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 4, 51, 526280)}
2025-07-30 18:09:52,292 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1391, 'successful_processed': 1391, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 9, 52, 241961)}
2025-07-30 18:09:52,292 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1391, 'successful_processed': 1391, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 9, 52, 241961)}
2025-07-30 18:14:53,065 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1401, 'successful_processed': 1401, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 14, 53, 5124)}
2025-07-30 18:14:53,065 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1401, 'successful_processed': 1401, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 14, 53, 5124)}
2025-07-30 18:19:53,942 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1411, 'successful_processed': 1411, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 19, 53, 890995)}
2025-07-30 18:19:53,942 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1411, 'successful_processed': 1411, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 19, 53, 890995)}
2025-07-30 18:24:54,789 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1421, 'successful_processed': 1421, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 24, 54, 744341)}
2025-07-30 18:24:54,789 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1421, 'successful_processed': 1421, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 24, 54, 744341)}
2025-07-30 18:29:55,746 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1431, 'successful_processed': 1431, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 29, 55, 687066)}
2025-07-30 18:29:55,746 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1431, 'successful_processed': 1431, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 29, 55, 687066)}
2025-07-30 18:34:56,529 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1441, 'successful_processed': 1441, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 34, 56, 483925)}
2025-07-30 18:34:56,529 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1441, 'successful_processed': 1441, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 34, 56, 483925)}
2025-07-30 18:39:57,206 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1451, 'successful_processed': 1451, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 39, 57, 162038)}
2025-07-30 18:39:57,206 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1451, 'successful_processed': 1451, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 39, 57, 162038)}
2025-07-30 18:44:58,126 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1461, 'successful_processed': 1461, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 44, 58, 48409)}
2025-07-30 18:44:58,126 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1461, 'successful_processed': 1461, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 44, 58, 48409)}
2025-07-30 18:49:58,909 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1471, 'successful_processed': 1471, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 49, 58, 862989)}
2025-07-30 18:49:58,909 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1471, 'successful_processed': 1471, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 49, 58, 862989)}
2025-07-30 18:54:59,818 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1481, 'successful_processed': 1481, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 54, 59, 748484)}
2025-07-30 18:54:59,818 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1481, 'successful_processed': 1481, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 18, 54, 59, 748484)}
2025-07-30 19:00:00,730 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1491, 'successful_processed': 1491, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 0, 0, 650269)}
2025-07-30 19:00:00,730 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1491, 'successful_processed': 1491, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 0, 0, 650269)}
2025-07-30 19:05:01,519 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1501, 'successful_processed': 1501, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 5, 1, 431818)}
2025-07-30 19:05:01,519 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1501, 'successful_processed': 1501, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 5, 1, 431818)}
2025-07-30 19:10:02,450 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1511, 'successful_processed': 1511, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 10, 2, 384782)}
2025-07-30 19:10:02,450 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1511, 'successful_processed': 1511, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 10, 2, 384782)}
2025-07-30 19:15:03,191 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1521, 'successful_processed': 1521, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 15, 3, 123181)}
2025-07-30 19:15:03,191 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1521, 'successful_processed': 1521, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 15, 3, 123181)}
2025-07-30 19:20:03,983 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1531, 'successful_processed': 1531, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 20, 3, 914265)}
2025-07-30 19:20:03,983 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1531, 'successful_processed': 1531, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 20, 3, 914265)}
2025-07-30 19:25:04,938 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1541, 'successful_processed': 1541, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 25, 4, 873882)}
2025-07-30 19:25:04,938 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1541, 'successful_processed': 1541, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 25, 4, 873882)}
2025-07-30 19:30:05,656 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1551, 'successful_processed': 1551, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 30, 5, 577720)}
2025-07-30 19:30:05,656 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1551, 'successful_processed': 1551, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 30, 5, 577720)}
2025-07-30 19:35:06,469 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1561, 'successful_processed': 1561, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 35, 6, 408335)}
2025-07-30 19:35:06,469 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1561, 'successful_processed': 1561, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 35, 6, 408335)}
2025-07-30 19:40:07,225 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1571, 'successful_processed': 1571, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 40, 7, 158760)}
2025-07-30 19:40:07,225 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1571, 'successful_processed': 1571, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 40, 7, 158760)}
2025-07-30 19:45:08,011 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1581, 'successful_processed': 1581, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 45, 7, 939677)}
2025-07-30 19:45:08,011 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1581, 'successful_processed': 1581, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 45, 7, 939677)}
2025-07-30 19:50:08,786 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1591, 'successful_processed': 1591, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 50, 8, 710231)}
2025-07-30 19:50:08,786 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1591, 'successful_processed': 1591, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 50, 8, 710231)}
2025-07-30 19:55:09,635 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1601, 'successful_processed': 1601, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 55, 9, 575405)}
2025-07-30 19:55:09,635 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1601, 'successful_processed': 1601, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 19, 55, 9, 575405)}
2025-07-30 20:00:10,459 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1611, 'successful_processed': 1611, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 20, 0, 10, 346951)}
2025-07-30 20:00:10,459 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1611, 'successful_processed': 1611, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 20, 0, 10, 346951)}
2025-07-30 20:05:11,239 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1621, 'successful_processed': 1621, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 20, 5, 11, 168493)}
2025-07-30 20:05:11,239 - EnhancedTimeframeProcessor - INFO - 📊 定期状态报告: {'total_processed': 1621, 'successful_processed': 1621, 'failed_processed': 0, 'success_rate': 100.0, 'data_quality_scores': {'5m': 1.0, '15m': 1.0, '1h': 1.0, '4h': 1.0}, 'last_processing_time': datetime.datetime(2025, 7, 30, 20, 5, 11, 168493)}
