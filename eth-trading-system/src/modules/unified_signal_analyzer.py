#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一信号分析器
功能：
1. 统一处理5m/15m/1h/4h时间周期
2. 基于1分钟数据聚合计算EMA和成交量
3. 计算动态支撑压力线
4. 生成各周期看涨/看跌信号
"""

import logging
import mysql.connector
import numpy as np
import pandas as pd
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

# 导入优化模块
from .enhanced_support_resistance import EnhancedSupportResistance
from .intelligent_signal_filter import IntelligentSignalFilter
from .adaptive_market_analyzer import AdaptiveMarketAnalyzer, MarketState
from .improved_kline_timing import ImprovedKlineTiming

class UnifiedSignalAnalyzer:
    """统一信号分析器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.db_config = config['database']
        self.logger = logging.getLogger(__name__)
        
        # 时间周期配置 - 调整为合理的数据要求
        self.timeframes = {
            '5m': {'minutes': 5, 'data_points': 3},      # 最少3个5分钟数据点
            '15m': {'minutes': 15, 'data_points': 5},    # 最少5个15分钟数据点
            '1h': {'minutes': 60, 'data_points': 8},     # 最少8个1小时数据点
            '4h': {'minutes': 240, 'data_points': 10}    # 最少10个4小时数据点
        }
        
        # 技术指标参数
        self.ema_config = {
            'fast': 12,  # 快线周期
            'slow': 26   # 慢线周期
        }
        
        # 成交量分析参数
        self.volume_config = {
            'ma_period': 20,      # 成交量移动平均周期
            'spike_threshold': 2.0,  # 放量阈值
            'dry_threshold': 0.5     # 缩量阈值
        }
        
        # 支撑压力线参数
        self.support_resistance_config = {
            'lookback_period': 50,   # 回看周期
            'min_touches': 2,        # 最少触及次数
            'price_tolerance': 0.002 # 价格容忍度 0.2%
        }
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 初始化优化模块
        self.enhanced_sr = EnhancedSupportResistance(config)
        self.signal_filter = IntelligentSignalFilter(config)
        self.market_analyzer = AdaptiveMarketAnalyzer(config)
        self.kline_timing = ImprovedKlineTiming(self.logger)

        # 优化开关 - 从optimizations节点读取
        optimizations = config.get('optimizations', {})
        self.use_enhanced_sr = optimizations.get('use_enhanced_support_resistance', True)
        self.use_intelligent_filter = optimizations.get('use_intelligent_filter', True)
        self.use_adaptive_params = optimizations.get('use_adaptive_parameters', True)
        self.use_improved_timing = optimizations.get('use_improved_timing', True)
        
    def get_db_connection(self):
        """获取数据库连接"""
        return mysql.connector.connect(
            host=self.db_config['host'],
            port=self.db_config['port'],
            user=self.db_config['user'],
            password=self.db_config['password'],
            database=self.db_config['database'],
            charset=self.db_config.get('charset', 'utf8mb4'),
            autocommit=True
        )
    
    def create_unified_signal_table(self):
        """创建统一信号表"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        try:
            signal_table_sql = """
            CREATE TABLE IF NOT EXISTS eth_unified_signals (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp BIGINT NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                signal_direction VARCHAR(10) NOT NULL,
                signal_strength DECIMAL(5,2) NOT NULL,
                confidence_score DECIMAL(5,2) NOT NULL,
                current_price DECIMAL(15,8) NOT NULL,
                ema_fast DECIMAL(15,8),
                ema_slow DECIMAL(15,8),
                ema_signal VARCHAR(20),
                volume_ratio DECIMAL(10,4),
                volume_signal VARCHAR(20),
                support_level DECIMAL(15,8),
                resistance_level DECIMAL(15,8),
                price_position VARCHAR(20),
                target_price DECIMAL(15,8),
                stop_loss_price DECIMAL(15,8),
                risk_reward_ratio DECIMAL(5,2),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_timestamp (timestamp),
                INDEX idx_timeframe (timeframe),
                INDEX idx_direction (signal_direction)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            cursor.execute(signal_table_sql)
            self.logger.info("统一信号表创建完成")
            
        finally:
            cursor.close()
            conn.close()
    
    def get_kline_data(self, data_points: int) -> pd.DataFrame:
        """获取指定数量的1分钟K线数据"""
        conn = self.get_db_connection()
        
        try:
            query = """
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM eth_spot_heartbeat_1m 
            ORDER BY timestamp DESC 
            LIMIT %s
            """
            
            df = pd.read_sql(query, conn, params=(data_points,))
            
            if not df.empty:
                df = df.sort_values('timestamp').reset_index(drop=True)
                # 确保数据类型正确
                for col in ['open_price', 'high_price', 'low_price', 'close_price', 'volume']:
                    df[col] = pd.to_numeric(df[col])
                
            return df
            
        finally:
            conn.close()
    
    def calculate_ema_indicators(self, df: pd.DataFrame) -> Dict:
        """计算EMA指标"""
        if len(df) < self.ema_config['slow']:
            return {'ema_fast': None, 'ema_slow': None, 'signal': 'insufficient_data'}
        
        # 计算EMA
        ema_fast = df['close_price'].ewm(span=self.ema_config['fast']).mean().iloc[-1]
        ema_slow = df['close_price'].ewm(span=self.ema_config['slow']).mean().iloc[-1]
        
        # 判断EMA信号
        if ema_fast > ema_slow:
            if len(df) > 1:
                prev_fast = df['close_price'].ewm(span=self.ema_config['fast']).mean().iloc[-2]
                prev_slow = df['close_price'].ewm(span=self.ema_config['slow']).mean().iloc[-2]
                
                if prev_fast <= prev_slow:
                    signal = 'golden_cross'  # 金叉
                else:
                    signal = 'bullish'       # 多头排列
            else:
                signal = 'bullish'
        else:
            if len(df) > 1:
                prev_fast = df['close_price'].ewm(span=self.ema_config['fast']).mean().iloc[-2]
                prev_slow = df['close_price'].ewm(span=self.ema_config['slow']).mean().iloc[-2]
                
                if prev_fast >= prev_slow:
                    signal = 'death_cross'   # 死叉
                else:
                    signal = 'bearish'       # 空头排列
            else:
                signal = 'bearish'
        
        return {
            'ema_fast': float(ema_fast),
            'ema_slow': float(ema_slow),
            'signal': signal
        }
    
    def calculate_volume_indicators(self, df: pd.DataFrame) -> Dict:
        """计算成交量指标"""
        if len(df) < self.volume_config['ma_period']:
            return {'volume_ratio': 1.0, 'signal': 'insufficient_data'}
        
        # 计算成交量移动平均
        volume_ma = df['volume'].rolling(window=self.volume_config['ma_period']).mean().iloc[-1]
        current_volume = df['volume'].iloc[-1]
        
        volume_ratio = current_volume / volume_ma if volume_ma > 0 else 1.0
        
        # 判断成交量信号
        if volume_ratio >= self.volume_config['spike_threshold']:
            signal = 'volume_spike'  # 放量
        elif volume_ratio <= self.volume_config['dry_threshold']:
            signal = 'volume_dry'    # 缩量
        else:
            signal = 'normal'        # 正常
        
        return {
            'volume_ratio': float(volume_ratio),
            'signal': signal
        }
    
    def calculate_support_resistance(self, df: pd.DataFrame) -> Dict:
        """计算动态支撑压力线"""
        if len(df) < self.support_resistance_config['lookback_period']:
            return {
                'support_level': None,
                'resistance_level': None,
                'position': 'unknown'
            }
        
        # 获取高低点
        highs = df['high_price'].values
        lows = df['low_price'].values
        current_price = df['close_price'].iloc[-1]
        
        # 寻找支撑位（低点）
        support_levels = []
        for i in range(2, len(lows) - 2):
            if (lows[i] <= lows[i-1] and lows[i] <= lows[i-2] and 
                lows[i] <= lows[i+1] and lows[i] <= lows[i+2]):
                support_levels.append(lows[i])
        
        # 寻找压力位（高点）
        resistance_levels = []
        for i in range(2, len(highs) - 2):
            if (highs[i] >= highs[i-1] and highs[i] >= highs[i-2] and 
                highs[i] >= highs[i+1] and highs[i] >= highs[i+2]):
                resistance_levels.append(highs[i])
        
        # 找到最近的支撑和压力位
        support_level = None
        resistance_level = None
        
        if support_levels:
            # 找到小于当前价格的最高支撑位
            valid_supports = [s for s in support_levels if s < current_price]
            if valid_supports:
                support_level = max(valid_supports)
        
        if resistance_levels:
            # 找到大于当前价格的最低压力位
            valid_resistances = [r for r in resistance_levels if r > current_price]
            if valid_resistances:
                resistance_level = min(valid_resistances)
        
        # 判断价格位置
        position = 'middle'
        if support_level and resistance_level:
            range_size = resistance_level - support_level
            if current_price - support_level < range_size * 0.3:
                position = 'near_support'
            elif resistance_level - current_price < range_size * 0.3:
                position = 'near_resistance'
        elif support_level and current_price - support_level < current_price * 0.02:
            position = 'near_support'
        elif resistance_level and resistance_level - current_price < current_price * 0.02:
            position = 'near_resistance'
        
        return {
            'support_level': float(support_level) if support_level else None,
            'resistance_level': float(resistance_level) if resistance_level else None,
            'position': position
        }
    
    def is_kline_completed(self, timeframe: str) -> bool:
        """检查指定时间周期的K线是否完结"""
        try:
            # 🚀 优化: 使用改进的K线时间对齐模块
            if self.use_improved_timing:
                return self.kline_timing.is_kline_completed(timeframe)

            # 保留原始逻辑作为备用
            current_time = datetime.now()
            current_minute = current_time.minute
            current_hour = current_time.hour
            current_second = current_time.second

            # 需要等待至少10秒确保K线数据已经到达
            if current_second < 10:
                return False

            if timeframe == '5m':
                # 5分钟K线在每5分钟的整数倍完结 (00:05, 00:10, 00:15...)
                return current_minute % 5 == 0
            elif timeframe == '15m':
                # 15分钟K线在每15分钟的整数倍完结 (00:00, 00:15, 00:30, 00:45)
                return current_minute % 15 == 0
            elif timeframe == '1h':
                # 1小时K线在每小时的0分完结 (01:00, 02:00, 03:00...)
                return current_minute == 0
            elif timeframe == '4h':
                # 4小时K线在每4小时的0分完结 (00:00, 04:00, 08:00, 12:00, 16:00, 20:00)
                return current_minute == 0 and current_hour % 4 == 0
            else:
                return True  # 默认允许分析

        except Exception as e:
            self.logger.error(f"检查K线完结状态失败: {e}")
            return True  # 出错时默认允许分析

    def analyze_timeframe_signal(self, timeframe: str) -> Optional[Dict]:
        """分析指定时间周期的信号"""
        try:
            # 检查K线是否完结
            if not self.is_kline_completed(timeframe):
                self.logger.debug(f"{timeframe} K线未完结，跳过分析")
                return None

            config = self.timeframes[timeframe]
            data_points = config['data_points']

            # 获取数据
            df = self.get_kline_data(data_points + 50)  # 多获取一些数据用于计算指标

            if len(df) < data_points:
                self.logger.warning(f"{timeframe} 数据不足: {len(df)}/{data_points}")
                return None
            
            # 只使用最近的指定数量数据点进行聚合
            recent_df = df.tail(data_points).copy()
            current_price = recent_df['close_price'].iloc[-1]
            
            # 🚀 优化1: 市场状态自适应分析
            market_condition = None
            if self.use_adaptive_params:
                market_condition = self.market_analyzer.analyze_market_condition(df, current_price)
                # 根据市场状态调整EMA参数
                if market_condition.state in [MarketState.HIGH_VOLATILITY, MarketState.BREAKOUT]:
                    self.ema_config['fast'] = market_condition.recommended_params['ema_fast']
                    self.ema_config['slow'] = market_condition.recommended_params['ema_slow']

            # 计算各种指标
            ema_result = self.calculate_ema_indicators(df)  # 使用全部数据计算EMA
            volume_result = self.calculate_volume_indicators(df)  # 使用全部数据计算成交量

            # 🚀 优化2: 使用增强型支撑压力线
            if self.use_enhanced_sr:
                sr_result = self.enhanced_sr.calculate_enhanced_support_resistance(df)
            else:
                sr_result = self.calculate_support_resistance(df)  # 原始方法

            # 综合判断信号方向和强度
            signal_direction, signal_strength, confidence = self.determine_signal(
                ema_result, volume_result, sr_result, current_price, market_condition
            )

            # 🚀 优化3: 智能信号过滤 - 降低阈值以适应当前市场
            if self.use_intelligent_filter and signal_strength >= 15:  # 降低阈值从30到15
                signal_data = {
                    'signal_direction': signal_direction,
                    'signal_strength': signal_strength,
                    'confidence': confidence,
                    'timeframe_analysis': {timeframe: {'signal_direction': signal_direction, 'trend_strength': confidence}},
                    'support_resistance': sr_result
                }

                market_data = {
                    'volume': recent_df['volume'].iloc[-1],
                    'candle_body_ratio': abs(recent_df['close_price'].iloc[-1] - recent_df['open_price'].iloc[-1]) /
                                       (recent_df['high_price'].iloc[-1] - recent_df['low_price'].iloc[-1]) if
                                       (recent_df['high_price'].iloc[-1] - recent_df['low_price'].iloc[-1]) > 0 else 0.5
                }

                filter_result = self.signal_filter.filter_signal(signal_data, market_data, df)

                if not filter_result.passed:
                    self.logger.info(f"{timeframe} 信号被过滤器拒绝: {filter_result.reason}")
                    return None

                # 使用过滤器评分调整置信度
                confidence = (confidence + filter_result.score) / 2

            elif signal_strength < 15:  # 信号强度太低 - 降低阈值从30到15
                self.logger.debug(f"{timeframe} 信号强度 {signal_strength} 低于阈值 15")
                return None

            # 🚀 优化4: 自适应止损止盈计算
            target_price, stop_loss_price, risk_reward = self.calculate_adaptive_targets(
                current_price, signal_direction, sr_result, market_condition
            )
            
            # 生成描述
            description = self.generate_description(
                timeframe, ema_result, volume_result, sr_result
            )
            
            return {
                'timestamp': int(recent_df['timestamp'].iloc[-1]),
                'timeframe': timeframe,
                'signal_direction': signal_direction,
                'signal_strength': signal_strength,
                'confidence_score': confidence,
                'current_price': float(current_price),
                'ema_fast': ema_result['ema_fast'],
                'ema_slow': ema_result['ema_slow'],
                'ema_signal': ema_result['signal'],
                'volume_ratio': volume_result['volume_ratio'],
                'volume_signal': volume_result['signal'],
                'support_level': sr_result['support_level'],
                'resistance_level': sr_result['resistance_level'],
                'price_position': sr_result['position'],
                'target_price': target_price,
                'stop_loss_price': stop_loss_price,
                'risk_reward_ratio': risk_reward,
                'description': description
            }
            
        except Exception as e:
            self.logger.error(f"{timeframe} 信号分析失败: {e}")
            return None

    def determine_signal(self, ema_result: Dict, volume_result: Dict,
                        sr_result: Dict, current_price: float, market_condition=None) -> Tuple[str, float, float]:
        """综合判断信号方向和强度"""
        bullish_score = 0
        bearish_score = 0

        # EMA信号评分
        if ema_result['signal'] == 'golden_cross':
            bullish_score += 40
        elif ema_result['signal'] == 'bullish':
            bullish_score += 20
        elif ema_result['signal'] == 'death_cross':
            bearish_score += 40
        elif ema_result['signal'] == 'bearish':
            bearish_score += 20

        # 成交量信号评分
        if volume_result['signal'] == 'volume_spike':
            # 放量需要结合价格方向
            if ema_result['signal'] in ['golden_cross', 'bullish']:
                bullish_score += 30  # 放量上涨
            elif ema_result['signal'] in ['death_cross', 'bearish']:
                bearish_score += 30  # 放量下跌
        elif volume_result['signal'] == 'volume_dry':
            # 缩量通常是趋势减弱信号
            bullish_score -= 10
            bearish_score -= 10

        # 支撑压力位置评分
        if sr_result['position'] == 'near_support':
            bullish_score += 25  # 接近支撑位，看涨
        elif sr_result['position'] == 'near_resistance':
            bearish_score += 25  # 接近压力位，看跌

        # 确定方向和强度
        if bullish_score > bearish_score:
            direction = 'bullish'
            strength = min(100, bullish_score)
        else:
            direction = 'bearish'
            strength = min(100, bearish_score)

        # 🚀 市场状态调整评分
        if market_condition:
            state_multiplier = 1.0
            if market_condition.state == MarketState.TRENDING_UP and direction == 'bullish':
                state_multiplier = 1.2
            elif market_condition.state == MarketState.TRENDING_DOWN and direction == 'bearish':
                state_multiplier = 1.2
            elif market_condition.state == MarketState.HIGH_VOLATILITY:
                state_multiplier = 0.8  # 高波动时降低信号强度
            elif market_condition.state == MarketState.BREAKOUT:
                state_multiplier = 1.5  # 突破时增强信号

            strength *= state_multiplier
            strength = min(100, strength)

        # 计算置信度
        total_score = bullish_score + bearish_score
        confidence = min(100, total_score * 0.8)  # 总分越高置信度越高

        # 市场状态置信度调整
        if market_condition:
            confidence = (confidence + market_condition.confidence * 100) / 2

        return direction, strength, confidence

    def calculate_targets(self, current_price: float, direction: str,
                         sr_result: Dict) -> Tuple[float, float, float]:
        """计算目标价格和止损"""
        if direction == 'bullish':
            # 看涨目标
            if sr_result['resistance_level']:
                target_price = sr_result['resistance_level']
            else:
                target_price = current_price * 1.02  # 默认2%目标

            # 看涨止损
            if sr_result['support_level']:
                stop_loss_price = sr_result['support_level']
            else:
                stop_loss_price = current_price * 0.98  # 默认2%止损
        else:
            # 看跌目标
            if sr_result['support_level']:
                target_price = sr_result['support_level']
            else:
                target_price = current_price * 0.98  # 默认2%目标

            # 看跌止损
            if sr_result['resistance_level']:
                stop_loss_price = sr_result['resistance_level']
            else:
                stop_loss_price = current_price * 1.02  # 默认2%止损

        # 计算风险收益比
        if direction == 'bullish':
            profit = target_price - current_price
            loss = current_price - stop_loss_price
        else:
            profit = current_price - target_price
            loss = stop_loss_price - current_price

        risk_reward = profit / loss if loss > 0 else 0

        return float(target_price), float(stop_loss_price), float(risk_reward)

    def calculate_adaptive_targets(self, current_price: float, direction: str,
                                 sr_result: Dict, market_condition=None) -> Tuple[float, float, float]:
        """🚀 自适应目标价格和止损计算"""
        # 获取基础参数
        if market_condition:
            stop_loss_ratio = market_condition.recommended_params['stop_loss_ratio']
            take_profit_ratio = market_condition.recommended_params['take_profit_ratio']
        else:
            stop_loss_ratio = 0.02  # 默认2%
            take_profit_ratio = 0.04  # 默认4%

        if direction == 'bullish':
            # 看涨目标 - 优先使用增强型支撑压力位
            if sr_result.get('resistance_level') and sr_result.get('resistance_strength', 0) > 0.6:
                target_price = sr_result['resistance_level']
                # 根据压力位强度调整目标
                if sr_result.get('resistance_strength', 0) > 0.8:
                    target_price *= 0.98  # 强压力位，目标稍微保守
            else:
                target_price = current_price * (1 + take_profit_ratio)

            # 看涨止损 - 优先使用支撑位
            if sr_result.get('support_level') and sr_result.get('support_strength', 0) > 0.6:
                stop_loss_price = sr_result['support_level'] * 0.995  # 支撑位下方0.5%
            else:
                stop_loss_price = current_price * (1 - stop_loss_ratio)
        else:
            # 看跌目标
            if sr_result.get('support_level') and sr_result.get('support_strength', 0) > 0.6:
                target_price = sr_result['support_level']
                if sr_result.get('support_strength', 0) > 0.8:
                    target_price *= 1.02  # 强支撑位，目标稍微保守
            else:
                target_price = current_price * (1 - take_profit_ratio)

            # 看跌止损
            if sr_result.get('resistance_level') and sr_result.get('resistance_strength', 0) > 0.6:
                stop_loss_price = sr_result['resistance_level'] * 1.005  # 压力位上方0.5%
            else:
                stop_loss_price = current_price * (1 + stop_loss_ratio)

        # 计算风险收益比
        if direction == 'bullish':
            risk = current_price - stop_loss_price
            reward = target_price - current_price
        else:
            risk = stop_loss_price - current_price
            reward = current_price - target_price

        risk_reward = reward / risk if risk > 0 else 0

        # 🚀 市场状态风险收益比调整
        if market_condition:
            min_risk_reward = market_condition.recommended_params['risk_reward_min']
            if risk_reward < min_risk_reward:
                # 调整目标价格以满足最小风险收益比
                if direction == 'bullish':
                    target_price = current_price + risk * min_risk_reward
                else:
                    target_price = current_price - risk * min_risk_reward
                risk_reward = min_risk_reward

        return float(target_price), float(stop_loss_price), float(risk_reward)

    def generate_description(self, timeframe: str, ema_result: Dict,
                           volume_result: Dict, sr_result: Dict) -> str:
        """生成信号描述"""
        descriptions = []

        # EMA描述
        if ema_result['signal'] == 'golden_cross':
            descriptions.append(f"{timeframe} EMA金叉")
        elif ema_result['signal'] == 'death_cross':
            descriptions.append(f"{timeframe} EMA死叉")
        elif ema_result['signal'] == 'bullish':
            descriptions.append(f"{timeframe} EMA多头排列")
        elif ema_result['signal'] == 'bearish':
            descriptions.append(f"{timeframe} EMA空头排列")

        # 成交量描述
        if volume_result['signal'] == 'volume_spike':
            descriptions.append(f"放量{volume_result['volume_ratio']:.1f}倍")
        elif volume_result['signal'] == 'volume_dry':
            descriptions.append(f"缩量至{volume_result['volume_ratio']:.1f}倍")

        # 位置描述
        if sr_result['position'] == 'near_support':
            descriptions.append("接近支撑位")
        elif sr_result['position'] == 'near_resistance':
            descriptions.append("接近压力位")

        return '; '.join(descriptions)

    def save_signal(self, signal: Dict):
        """保存信号到数据库"""
        conn = self.get_db_connection()
        cursor = conn.cursor()

        try:
            insert_sql = """
            INSERT INTO eth_unified_signals
            (timestamp, timeframe, signal_direction, signal_strength, confidence_score,
             current_price, ema_fast, ema_slow, ema_signal, volume_ratio, volume_signal,
             support_level, resistance_level, price_position, target_price,
             stop_loss_price, risk_reward_ratio, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            data = (
                signal['timestamp'],
                signal['timeframe'],
                signal['signal_direction'],
                signal['signal_strength'],
                signal['confidence_score'],
                signal['current_price'],
                signal['ema_fast'],
                signal['ema_slow'],
                signal['ema_signal'],
                signal['volume_ratio'],
                signal['volume_signal'],
                signal['support_level'],
                signal['resistance_level'],
                signal['price_position'],
                signal['target_price'],
                signal['stop_loss_price'],
                signal['risk_reward_ratio'],
                signal['description']
            )

            cursor.execute(insert_sql, data)

            self.logger.info(f"📊 {signal['timeframe']} 信号: {signal['signal_direction']} "
                           f"强度:{signal['signal_strength']:.1f} "
                           f"置信度:{signal['confidence_score']:.1f} "
                           f"价格:{signal['current_price']:.2f}→{signal['target_price']:.2f}")

        except Exception as e:
            self.logger.error(f"保存{signal['timeframe']}信号失败: {e}")
        finally:
            cursor.close()
            conn.close()

    def analyze_all_timeframes(self) -> List[Dict]:
        """并行分析所有时间周期"""
        # 使用线程池并行处理
        future_to_timeframe = {
            self.executor.submit(self.analyze_timeframe_signal, tf): tf
            for tf in self.timeframes.keys()
        }

        signals = []

        for future in future_to_timeframe:
            timeframe = future_to_timeframe[future]
            try:
                signal = future.result(timeout=30)
                if signal:
                    signals.append(signal)
                    # 保存信号
                    self.save_signal(signal)
                else:
                    self.logger.info(f"{timeframe} 无有效信号")
            except Exception as e:
                self.logger.error(f"{timeframe} 信号分析异常: {e}")

        return signals

    def get_latest_signals(self, limit: int = 10) -> List[Dict]:
        """获取最新信号"""
        conn = self.get_db_connection()
        cursor = conn.cursor()

        try:
            query = """
            SELECT timeframe, signal_direction, signal_strength, confidence_score,
                   current_price, target_price, description,
                   FROM_UNIXTIME(timestamp/1000) as signal_time
            FROM eth_unified_signals
            ORDER BY timestamp DESC
            LIMIT %s
            """

            cursor.execute(query, (limit,))
            results = cursor.fetchall()

            signals = []
            for row in results:
                signals.append({
                    'timeframe': row[0],
                    'direction': row[1],
                    'strength': float(row[2]),
                    'confidence': float(row[3]),
                    'current_price': float(row[4]),
                    'target_price': float(row[5]),
                    'description': row[6],
                    'time': row[7]
                })

            return signals

        finally:
            cursor.close()
            conn.close()

    def run_single_analysis(self):
        """运行单次分析"""
        self.logger.info("🔄 开始单次信号分析...")

        signals = self.analyze_all_timeframes()

        if signals:
            self.logger.info(f"✅ 分析完成，生成 {len(signals)} 个信号")

            # 显示信号摘要
            for signal in signals:
                print(f"📊 {signal['timeframe']}: {signal['signal_direction']} "
                      f"强度:{signal['signal_strength']:.1f} "
                      f"价格:{signal['current_price']:.2f}→{signal['target_price']:.2f}")
        else:
            self.logger.info("📊 当前无有效信号")

        return signals
